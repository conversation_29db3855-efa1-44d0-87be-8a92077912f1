

import javax.crypto.Cipher;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import javax.crypto.spec.SecretKeySpec;

import sun.misc.BASE64Encoder;

import javax.net.ssl.SSLHandshakeException;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.*;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.*;

public class ApiClient {

    private static String url = "https://test-open-api.open.chinaums.com/v2/token/access";
    private static String url2 = "https://test-api-open.chinaums.com/agent-collector/agentcollect";
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    private static final String key = "jGe&jpQIOJ#yv^Rv";

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        System.out.print("请输入银商AppId: ");
        String appId = scanner.nextLine();
        while (isValid(appId)) {
            System.out.println("AppId不符合要求。请重新输入");
            System.out.print("请重新输入AppId: ");
            appId = scanner.nextLine();
        }
        String appkey = "testAppkey";
        String nonce = UUID.randomUUID().toString().replace("-", "");
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        //针对jdk1.6特殊处理
        if (isJdkVersionLowerThan16()) {
            System.err.println("JAVA版本低、根证书报错：请升级JDK到1.8.0.131及以上。");
            String remark1 = "JAVA版本低、根证书报错：请升级JDK到1.8.0.131及以上。";
            ArrayList<Object> remark = new ArrayList<Object>();
            remark.add(remark1);
            remark.add("false");
            CheckSupported(appId, nonce, timestamp, remark);
        } else {
            ArrayList<Object> remark = getToken(appId, appkey, nonce, timestamp);
            CheckSupported(appId, nonce, timestamp, remark);
        }
    }

    public static boolean isValid(String appId) {
        return appId.length() != 32 || (!appId.startsWith("8") && !appId.startsWith("4"));
    }

    public static String toJson(Map<String, Object> map) {
        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!first) {
                json.append(",");
            }
            json.append("\"").append(entry.getKey()).append("\":");
            Object value = entry.getValue();
            if (value instanceof String) {
                json.append("\"").append(value).append("\"");
            } else if (value instanceof Map) {
                json.append(toJson((Map<String, Object>) value));
            } else if (value instanceof List) {
                json.append(toJsonArray((List<?>) value));
            } else {
                json.append(value);
            }
            first = false;
        }
        json.append("}");
        return json.toString();
    }

    public static String toJsonArray(List<?> list) {
        StringBuilder jsonArray = new StringBuilder("[");
        boolean first = true;
        for (Object item : list) {
            if (!first) {
                jsonArray.append(",");
            }
            if (item instanceof String) {
                jsonArray.append("\"").append(item).append("\"");
            } else if (item instanceof Map) {
                jsonArray.append(toJson((Map<String, Object>) item));
            } else {
                jsonArray.append(item);
            }
            first = false;
        }
        jsonArray.append("]");
        return jsonArray.toString();
    }

    public static ArrayList getToken(String appId, String appKey, String nonce, String timestamp) {
        Map<String, Object> jsonObject = new HashMap<String, Object>();
        String all = appId + timestamp + nonce + appKey;
        String signature = sha256(all);
        jsonObject.put("appId", appId);
        jsonObject.put("timestamp", timestamp);
        jsonObject.put("nonce", nonce);
        jsonObject.put("signature", signature);
        jsonObject.put("signMethod", "SHA256");
        ArrayList remark = send(url, toJson(jsonObject));
        return remark;
    }

    public static boolean isJdkVersionLowerThan16() {
        String version = System.getProperty("java.version");
        if (version.startsWith("1.")) {
            int majorVersion = Integer.parseInt(version.substring(2, 3));
            return majorVersion <= 6;
        }
        return false;
    }

    public static String sha256(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder(2 * hash.length);
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public static String encrypt(String plainText, String key) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes("UTF-8"), ALGORITHM);
            Cipher cipher = null;
            cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes("UTF-8"));
            return encode(encryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String encode(byte[] data) {
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(data);
    }

    public static ArrayList send(String u, String entity) {
        HttpURLConnection connection = null;
        InputStream is = null;
        OutputStream out = null;
        BufferedReader br = null;
        String resultStr = "";
        String remark1 = "";
        Map<String, String> resultMap = new HashMap<String, String>();
        ArrayList<Object> remark = new ArrayList<Object>();
        try {
            URL url = new URL(u);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setRequestProperty("Accept_Charset", "UTF-8");
            connection.setRequestProperty("Content-Type", "application/json");
            out = connection.getOutputStream();
            out.write(entity.getBytes("UTF-8"));
            out.flush();
            StringBuffer sbf = new StringBuffer();
            if (connection.getResponseCode() == 200) {
                is = connection.getInputStream();
                br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                String temp = null;
                while ((temp = br.readLine()) != null) {
                    sbf.append(temp);
                }
            } else {
                try {
                    is = connection.getErrorStream();
                    br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                    String temp = null;
                    while ((temp = br.readLine()) != null) {
                        sbf.append(temp);
                    }
                    resultStr = sbf.toString();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            resultStr = sbf.toString();
            System.out.println("验证成功");
            remark1 = "验证成功";
            remark.add(remark1);
            remark.add("true");
        } catch (SSLHandshakeException e) {
            System.err.println("JAVA版本低、根证书报错：请升级JDK到1.8.0.131及以上。");
            remark1 = "JAVA版本低、根证书报错：请升级JDK到1.8.0.131及以上。";
            remark.add(remark1);
            remark.add("false");
        } catch (IOException e) {
            System.err.println("网络不通");
            remark1 = "网络不通";
            remark.add(remark1);
            remark.add("false");
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            connection.disconnect();
        }
        return remark;
    }

    public static String redict(String u, String entity) {
        HttpURLConnection connection = null;
        InputStream is = null;
        OutputStream out = null;
        BufferedReader br = null;
        String resultStr = null;
        Map<String, String> resultMap = new HashMap<String, String>();
        try {
            URL url = new URL(u);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setRequestProperty("Accept_Charset", "UTF-8");
            connection.setRequestProperty("Content-Type", "application/json");
            out = connection.getOutputStream();
            out.write(entity.getBytes("UTF-8"));
            out.flush();
            StringBuffer sbf = new StringBuffer();
            if (connection.getResponseCode() == 200) {
                is = connection.getInputStream();
                br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                String temp = null;
                while ((temp = br.readLine()) != null) {
                    sbf.append(temp);
                }
            } else {
                try {
                    is = connection.getErrorStream();
                    br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                    String temp = null;
                    while ((temp = br.readLine()) != null) {
                        sbf.append(temp);
                    }
                    resultStr = sbf.toString();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            resultStr = sbf.toString();
        } catch (IOException e) {
        } finally {
            try {
                if (out != null) {
                    out.close();
                }

            } catch (Exception ex) {
                ex.printStackTrace();
            }
            connection.disconnect();
        }
        return resultStr;
    }

    public static void CheckSupported(String appId, String nonce, String timestamp, ArrayList remark) {
        Map<String, Object> jsonObject = new HashMap<String, Object>();
        jsonObject.put("appId", appId);
        jsonObject.put("requestTime", timestamp);
        jsonObject.put("nonce", nonce);
        jsonObject.put("progRuntimeEnv", System.getProperty("java.version"));
        jsonObject.put("OS", System.getProperty("os.name"));
        jsonObject.put("progLang", "JAVA");
        String remark1 = remark.get(0).toString();
        String result = remark.get(1).toString();
        jsonObject.put("remark1", remark1);
        jsonObject.put("result", result);
        List jdkCertVersionArray = new ArrayList<Object>();
        try {
            String javaHome = System.getProperty("java.home");
            String cacertsPath = javaHome + File.separator + "lib" + File.separator + "security" + File.separator + "cacerts";
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            try {
                FileInputStream fis = new FileInputStream(cacertsPath);
                keyStore.load(fis, "changeit".toCharArray());
            } catch (Exception e) {
                e.printStackTrace();
            }
            Enumeration<String> aliases = keyStore.aliases();
            while (aliases.hasMoreElements()) {
                String alias = aliases.nextElement();
                Certificate cert = keyStore.getCertificate(alias);
                if (cert instanceof X509Certificate) {
                    X509Certificate x509Cert = (X509Certificate) cert;
                    Map<String, String> jdkCertVersion = new HashMap<String, String>();
                    jdkCertVersion.put("alias", alias);
                    String issuerDN = x509Cert.getSubjectDN().getName().replaceAll("\"", "\\\\\"");
                    jdkCertVersion.put("issuerDN", issuerDN);
                    String subjectDN = x509Cert.getSubjectDN().getName().replaceAll("\"", "\\\\\"");
                    jdkCertVersion.put("subjectDN", subjectDN);
                    jdkCertVersion.put("notBefore", x509Cert.getNotBefore().toString());
                    jdkCertVersion.put("notAfter", x509Cert.getNotAfter().toString());
                    jdkCertVersion.put("version", x509Cert.getVersion() + "");
                    jdkCertVersionArray.add(jdkCertVersion);
                }
            }
            //jsonObject.put("remark2",jdkCertVersionArray);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String encryptedText = encrypt(toJson(jsonObject), key);
        encryptedText = encryptedText.replaceAll("\r|\n", "");
        Map<String, Object> redict = new HashMap<String, Object>();
        redict.put("str", encryptedText);
        String redictStr = toJson(redict);
        redict(url2, redictStr);
    }
}